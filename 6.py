# pip install edge-tts
import asyncio
import edge_tts

VOICE = "zh-CN-YunxiNeural"
RATE = "-10%"

TEXT = """111在今天的讲座中，我们将讨论如何将控制问题重构成一个推理问题。我们实际上会看到，上周关于变分推理讲座中的一些思想，在今天的讲座中会作为解决强化学习问题的方法出现，一旦这些问题被重构成推理问题。"""

async def play_now(text: str, voice: str = VOICE, rate: str = RATE) -> bool:
    """
    立即播放，不保存；播放完成后返回 True（成功）/False（失败）
    需要系统已安装 `mpv`
    """
    tts = edge_tts.Communicate(text, voice=voice, rate=rate)

    # 启动 mpv，从标准输入读取音频；用 "-" 代表 stdin（跨平台）
    proc = await asyncio.create_subprocess_exec(
        "mpv", "--no-video", "--really-quiet", "--", "-",  # 读取 stdin
        stdin=asyncio.subprocess.PIPE,
        stdout=asyncio.subprocess.DEVNULL,
        stderr=asyncio.subprocess.DEVNULL,
    )
    try:
        async for chunk in tts.stream():
            if chunk["type"] == "audio":
                proc.stdin.write(chunk["data"])
                await proc.stdin.drain()
        # 发送 EOF，等待 mpv 正常退出
        proc.stdin.close()
        rc = await proc.wait()
        return rc == 0
    finally:
        if proc.returncode is None:
            proc.kill()

if __name__ == "__main__":
    ok = asyncio.run(play_now(TEXT))
    print("播放完成：", ok)

########################################################################### pip install -U edge-tts
# import asyncio, edge_tts

# VOICE = "zh-CN-YunxiNeural"
# RATE  = "+5%"
# FORMAT = "riff-24khz-16bit-mono-pcm"  # 关键：改成 WAV/PCM

# async def play_now(text: str) -> bool:
#     tts = edge_tts.Communicate(text, voice=VOICE, rate=RATE, format=FORMAT)
#     # 先拿到首个音频块，确保 mpv 启动时就有“完整文件头”
#     first = None
#     agen = tts.stream()
#     async for chunk in agen:
#         if chunk["type"] == "audio":
#             first = chunk["data"]
#             break
#     if first is None:
#         return False

#     proc = await asyncio.create_subprocess_exec(
#         "mpv", "--no-video", "--really-quiet", "--", "-",
#         stdin=asyncio.subprocess.PIPE,
#         stdout=asyncio.subprocess.DEVNULL,
#         stderr=asyncio.subprocess.DEVNULL,
#     )

#     try:
#         proc.stdin.write(first)
#         await proc.stdin.drain()
#         # 继续把后续块写给 mpv
#         async for chunk in agen:
#             if chunk["type"] == "audio":
#                 proc.stdin.write(chunk["data"])
#                 await proc.stdin.drain()
#         proc.stdin.close()
#         rc = await proc.wait()
#         return rc == 0
#     finally:
#         if proc.returncode is None:
#             proc.kill()

# if __name__ == "__main__":
#     ok = asyncio.run(play_now(TEXT))
#     print("播放完成：", ok)